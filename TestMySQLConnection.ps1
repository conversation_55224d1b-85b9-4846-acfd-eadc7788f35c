Write-Host "Testing MySQL connection to ***********:3306"

try {
    $client = New-Object System.Net.Sockets.TcpClient
    $connectionResult = $client.BeginConnect("***********", 3306, $null, $null)
    $success = $connectionResult.AsyncWaitHandle.WaitOne(1000, $false)
    
    if ($success) {
        Write-Host "TCP connection to MySQL server at ***********:3306 was successful."
        Write-Host "This means the server is reachable but doesn't verify login credentials."
        $client.EndConnect($connectionResult)
    } else {
        Write-Host "TCP connection to MySQL server failed. The server might be down or unreachable."
    }
} catch {
    Write-Host "Error testing connection: $_"
} finally {
    if ($client -ne $null) {
        $client.Close()
    }
} 