# 测试MySQL MCP服务器配置
Write-Host "Testing MySQL MCP Server Configuration..." -ForegroundColor Green

# 设置环境变量
$env:MYSQL_HOST = "***********"
$env:MYSQL_PORT = "3306"
$env:MYSQL_USER = "root"
$env:MYSQL_PASS = "44033@lzd"
$env:MYSQL_DB = "bailun"
$env:ALLOW_INSERT_OPERATION = "true"
$env:ALLOW_UPDATE_OPERATION = "true"
$env:ALLOW_DELETE_OPERATION = "false"

Write-Host "Environment variables set:" -ForegroundColor Yellow
Write-Host "MYSQL_HOST: $env:MYSQL_HOST"
Write-Host "MYSQL_PORT: $env:MYSQL_PORT"
Write-Host "MYSQL_USER: $env:MYSQL_USER"
Write-Host "MYSQL_DB: $env:MYSQL_DB"
Write-Host "ALLOW_INSERT_OPERATION: $env:ALLOW_INSERT_OPERATION"
Write-Host "ALLOW_UPDATE_OPERATION: $env:ALLOW_UPDATE_OPERATION"
Write-Host "ALLOW_DELETE_OPERATION: $env:ALLOW_DELETE_OPERATION"
Write-Host ""

Write-Host "Testing MCP Server with Inspector..." -ForegroundColor Green
npx @modelcontextprotocol/inspector npx mcprunner MYSQL_HOST=*********** MYSQL_PORT=3306 MYSQL_USER=root MYSQL_PASS=44033@lzd MYSQL_DB=bailun ALLOW_INSERT_OPERATION=true ALLOW_UPDATE_OPERATION=true ALLOW_DELETE_OPERATION=false -- npx -y @benborla29/mcp-server-mysql
