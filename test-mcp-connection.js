#!/usr/bin/env node

// 简单的MCP客户端测试脚本
const { spawn } = require('child_process');

console.log('Testing MySQL MCP Server Connection...');

// 设置环境变量
const env = {
  ...process.env,
  MYSQL_HOST: '***********',
  MYSQL_PORT: '3306',
  MYSQL_USER: 'root',
  MYSQL_PASS: '44033@lzd',
  MYSQL_DB: 'bailun',
  ALLOW_INSERT_OPERATION: 'true',
  ALLOW_UPDATE_OPERATION: 'true',
  ALLOW_DELETE_OPERATION: 'false'
};

console.log('Environment variables:');
console.log(`MYSQL_HOST: ${env.MYSQL_HOST}`);
console.log(`MYSQL_PORT: ${env.MYSQL_PORT}`);
console.log(`MYSQL_USER: ${env.MYSQL_USER}`);
console.log(`MYSQL_DB: ${env.MYSQL_DB}`);
console.log('');

// 启动MCP服务器
const mcpServer = spawn('npx', ['-y', 'mcp-mysql-server'], {
  env: env,
  stdio: ['pipe', 'pipe', 'pipe']
});

let output = '';
let errorOutput = '';

mcpServer.stdout.on('data', (data) => {
  output += data.toString();
  console.log('STDOUT:', data.toString().trim());
});

mcpServer.stderr.on('data', (data) => {
  errorOutput += data.toString();
  console.log('STDERR:', data.toString().trim());
});

mcpServer.on('close', (code) => {
  console.log(`MCP server exited with code ${code}`);
  if (code === 0) {
    console.log('✅ MCP server started successfully');
  } else {
    console.log('❌ MCP server failed to start');
    console.log('Error output:', errorOutput);
  }
});

mcpServer.on('error', (error) => {
  console.log('❌ Failed to start MCP server:', error.message);
});

// 发送一个简单的MCP请求来测试连接
setTimeout(() => {
  console.log('Sending test request...');
  
  const testRequest = {
    jsonrpc: '2.0',
    id: 1,
    method: 'initialize',
    params: {
      protocolVersion: '2024-11-05',
      capabilities: {},
      clientInfo: {
        name: 'test-client',
        version: '1.0.0'
      }
    }
  };
  
  mcpServer.stdin.write(JSON.stringify(testRequest) + '\n');
}, 2000);

// 清理
setTimeout(() => {
  console.log('Terminating test...');
  mcpServer.kill();
}, 10000);
